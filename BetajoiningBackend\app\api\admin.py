import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.dependencies import get_db
from app.schemas.admin import AdminDashboardResponse, AdminUsersResponse, AdminUser
from app.models.user import User
from fastapi.security import OAuth2<PERSON>asswordBearer
from datetime import datetime, timedelta
from app.services.user_service import authenticate_user, create_jwt_for_user, get_user_by_id, promote_user_to_admin, get_bulk_email_recipients
from app.core.security import get_current_admin
from app.schemas.user import UserLogin
from app.tasks.email_tasks import send_bulk_email_task
from pydantic import BaseModel
from app.utils.monitoring import inc_bulk_email_sent, inc_admin_promotion

router = APIRouter(prefix="/admin", tags=["admin"])

class BulkEmailRequest(BaseModel):
    subject: str
    body: str
    min_points: int = 0

class PromoteRequest(BaseModel):
    user_id: int

@router.post("/login")
def admin_login(user_in: UserLogin, db: Session = Depends(get_db)):
    """Admin login. Returns JWT if credentials are valid and user is admin."""
    user = authenticate_user(db, user_in.email, user_in.password)
    if not user or not user.is_admin:
        raise HTTPException(status_code=403, detail="Admin credentials required")
    token = create_jwt_for_user(user)
    return {"access_token": token, "token_type": "bearer", "expires_in": 3600}

@router.get("/dashboard", response_model=AdminDashboardResponse)
def dashboard(db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    """Get admin dashboard overview with user and platform stats."""
    total_users = db.query(User).count()
    active_users_24h = db.query(User).filter(User.updated_at > datetime.utcnow() - timedelta(hours=24)).count()
    total_shares_today = 0  # Add real query
    points_distributed_today = 0  # Add real query
    platform_breakdown = {"facebook": {"shares": 0, "percentage": 0}, "twitter": {"shares": 0, "percentage": 0}, "linkedin": {"shares": 0, "percentage": 0}}
    growth_metrics = {"new_users_7d": 0, "user_retention_rate": 0, "average_session_duration": 0}
    return AdminDashboardResponse(
        overview={"total_users": total_users, "active_users_24h": active_users_24h, "total_shares_today": total_shares_today, "points_distributed_today": points_distributed_today},
        platform_breakdown=platform_breakdown,
        growth_metrics=growth_metrics
    )

@router.get("/users", response_model=AdminUsersResponse)
def admin_users(db: Session = Depends(get_db), admin=Depends(get_current_admin), page: int = 1, limit: int = 50, search: str = "", sort: str = "points"):
    """List users for admin panel with pagination, search, and sorting."""
    q = db.query(User)
    if search:
        q = q.filter(User.name.ilike(f"%{search}%"))
    if sort == "points":
        q = q.order_by(User.total_points.desc())
    total = q.count()
    users = q.offset((page-1)*limit).limit(limit).all()
    items = [AdminUser(user_id=u.id, name=u.name, email=u.email, points=u.total_points, rank=None, shares_count=u.shares_count, status="active" if u.is_active else "inactive", last_activity=u.updated_at, created_at=u.created_at) for u in users]
    return AdminUsersResponse(users=items, pagination={"page": page, "limit": limit, "total": total, "pages": (total+limit-1)//limit})

@router.post("/send-bulk-email")
def send_bulk_email(req: BulkEmailRequest, db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    """Send a bulk email to all or filtered users (min_points)."""
    users = get_bulk_email_recipients(db, req.min_points)
    emails = [u.email for u in users]
    if not emails:
        raise HTTPException(status_code=404, detail="No users found for criteria")
    send_bulk_email_task.delay(emails, req.subject, req.body)
    inc_bulk_email_sent()
    logging.info(f"Admin {admin['user_id']} sent bulk email to {len(emails)} users.")
    return {"message": f"Bulk email sent to {len(emails)} users (task queued)"}

@router.post("/promote")
def promote_user(req: PromoteRequest, db: Session = Depends(get_db), admin=Depends(get_current_admin)):
    """Promote a user to admin status."""
    user = promote_user_to_admin(db, req.user_id)
    inc_admin_promotion()
    logging.info(f"Admin {admin['user_id']} promoted user {user.email} to admin.")
    return {"message": f"User {user.email} promoted to admin."} 