/* Campaign Management Component Styles */

.campaign-management {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.campaign-tabs {
  border-bottom: 1px solid #e5e7eb;
}

.campaign-tabs nav {
  display: flex;
  gap: 2rem;
  padding: 0 1.5rem;
}

.campaign-tab {
  padding: 1rem 0.25rem;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  font-size: 0.875rem;
  color: #6b7280;
  background: none;
  border-left: none;
  border-right: none;
  border-top: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.campaign-tab:hover {
  color: #374151;
  border-bottom-color: #d1d5db;
}

.campaign-tab.active {
  color: #2563eb;
  border-bottom-color: #2563eb;
}

.campaign-content {
  padding: 1.5rem;
}

.campaign-card {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
  background: white;
  transition: all 0.2s ease;
}

.campaign-card:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.campaign-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.campaign-info {
  flex: 1;
}

.campaign-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.25rem;
}

.campaign-name {
  font-weight: 500;
  color: #111827;
  text-transform: capitalize;
}

.campaign-status {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.status-active {
  background-color: #dcfce7;
  color: #166534;
}

.status-scheduled {
  background-color: #f3f4f6;
  color: #374151;
}

.status-due {
  background-color: #fed7aa;
  color: #9a3412;
}

.status-completed {
  background-color: #dcfce7;
  color: #166534;
}

.status-failed {
  background-color: #fecaca;
  color: #991b1b;
}

.campaign-subject {
  font-size: 0.875rem;
  color: #4b5563;
  margin-top: 0.25rem;
}

.campaign-schedule {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.test-button {
  margin-left: 1rem;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #2563eb;
  background-color: #eff6ff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.test-button:hover {
  background-color: #dbeafe;
}

.test-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.send-button {
  padding: 0.5rem 1.5rem;
  background-color: #2563eb;
  color: white;
  font-weight: 500;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.send-button:hover {
  background-color: #1d4ed8;
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.send-button.full-width {
  width: 100%;
}

@media (min-width: 768px) {
  .send-button.full-width {
    width: auto;
  }
}

.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.alert-success {
  background-color: #dcfce7;
  border: 1px solid #bbf7d0;
  color: #166534;
}

.alert-warning {
  background-color: #fef3c7;
  border: 1px solid #fed7aa;
  color: #92400e;
}

.alert-error {
  background-color: #fecaca;
  border: 1px solid #fca5a5;
  color: #991b1b;
}

.alert-info {
  background-color: #dbeafe;
  border: 1px solid #93c5fd;
  color: #1e40af;
}

.preview-section {
  margin-bottom: 1rem;
  padding: 1rem;
  border-radius: 8px;
}

.preview-instant {
  background-color: #dcfce7;
  border: 1px solid #bbf7d0;
}

.preview-future {
  background-color: #dbeafe;
  border: 1px solid #93c5fd;
}

.preview-past {
  background-color: #f3f4f6;
  border: 1px solid #d1d5db;
}

.preview-title {
  font-weight: 500;
  margin-bottom: 0.25rem;
}

.preview-instant .preview-title {
  color: #166534;
}

.preview-future .preview-title {
  color: #1e40af;
}

.preview-past .preview-title {
  color: #374151;
}

.preview-note {
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

.preview-instant .preview-note {
  color: #15803d;
}

.preview-future .preview-note {
  color: #2563eb;
}

.preview-past .preview-note {
  color: #6b7280;
}

.campaign-item {
  background: white;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

.campaign-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.campaign-item-title {
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
}

.campaign-item-schedule {
  font-size: 0.875rem;
  color: #4b5563;
}

.days-badge {
  font-size: 0.75rem;
  background-color: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.refresh-button {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #2563eb;
  background-color: #eff6ff;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-button:hover {
  background-color: #dbeafe;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 16rem;
}
