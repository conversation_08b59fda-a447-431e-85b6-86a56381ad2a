.button {
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
  padding: 20px 32px;
  border: 2px solid transparent;
  cursor: pointer;
  font-family: 'Merriweather', serif;
  font-weight: bold;
  letter-spacing: 0.05em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* Smooth background transition effect */
.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: -1;
}

.button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(146, 125, 112, 0.3);
}

.button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(146, 125, 112, 0.2);
}

/* Variants */
.button--primary {
  background-color: #927D70;
  color: white;
  border-color: #927D70;
}

.button--primary::before {
  background-color: #927D70;
}

.button--primary:hover {
  background-color: transparent;
  color: #927D70;
  border-color: #927D70;
}

.button--primary:hover::before {
  background-color: transparent;
}

.button--secondary {
  background-color: transparent;
  color: #966F33;
  border-color: #927D70;
}

.button--secondary:hover {
  background-color: #927D70;
  color: white;
  border-color: #927D70;
}

/* Disabled state */
.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.button:disabled:hover {
  background-color: #927D70;
  color: white;
  border-color: #927D70;
  transform: none;
  box-shadow: none;
}

.button--secondary:disabled:hover {
  background-color: transparent;
  color: #966F33;
  border-color: #927D70;
}

/* Sizes */
.button--small {
  padding: 12px 24px;
  font-size: 14px;
  line-height: 16px;
}

.button--medium {
  padding: 20px 32px;
  font-size: 16px;
  line-height: 16px;
  height: 59px;
  min-width: 156px;
}

.button--large {
  padding: 20px 32px;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 0.1em;
  height: 65px;
  min-width: 206px;
}

/* Responsive */
@media (max-width: 640px) {
  .button--medium,
  .button--large {
    width: 100%;
  }
}
