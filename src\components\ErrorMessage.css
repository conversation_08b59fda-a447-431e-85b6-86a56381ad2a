.error-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border-radius: 0;
  border: 1px solid;
  background: white;
  margin: 20px 0;
}

.error-message.error {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.error-message.warning {
  border-color: #f39c12;
  background: #fef9e7;
}

.error-message.info {
  border-color: #3498db;
  background: #f0f8ff;
}

.error-content {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  width: 100%;
  max-width: 500px;
}

.error-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
  margin-top: 2px;
}

.error-text {
  flex: 1;
}

.error-title {
  font-family: 'Battambang', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #1a1a1a;
}

.error-description {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 0.95rem;
  margin: 0;
  line-height: 1.5;
  color: #666;
}

.error-retry-btn {
  margin-top: 15px;
  background: #d4af37;
  color: #1a1a1a;
  border: none;
  padding: 8px 16px;
  border-radius: 0;
  font-family: 'Merriweather', serif;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.error-retry-btn:hover {
  background: #f4d03f;
  transform: translateY(-1px);
}

/* Compact variant for inline errors */
.error-message.compact {
  padding: 10px 15px;
  margin: 10px 0;
  flex-direction: row;
  align-items: center;
}

.error-message.compact .error-content {
  max-width: none;
  margin-bottom: 0;
}

.error-message.compact .error-icon {
  font-size: 1.2rem;
  margin-top: 0;
}

.error-message.compact .error-title {
  font-size: 1rem;
  margin-bottom: 4px;
}

.error-message.compact .error-description {
  font-size: 0.85rem;
}

.error-message.compact .error-retry-btn {
  margin-top: 0;
  margin-left: 15px;
  padding: 6px 12px;
  font-size: 0.8rem;
}
