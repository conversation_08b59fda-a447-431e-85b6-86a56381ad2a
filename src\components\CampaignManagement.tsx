import React, { useState, useEffect } from 'react';
import { campaignService } from '../services/campaignService';
import { 
  CampaignScheduleResponse, 
  CampaignSendRequest, 
  NewUserCampaignsResponse 
} from '../types/api';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

const CampaignManagement: React.FC = () => {
  const [schedule, setSchedule] = useState<CampaignScheduleResponse | null>(null);
  const [newUserPreview, setNewUserPreview] = useState<NewUserCampaignsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sendingCampaign, setSendingCampaign] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'schedule' | 'send' | 'preview'>('schedule');

  // Form states
  const [selectedCampaign, setSelectedCampaign] = useState('welcome');
  const [sendType, setSendType] = useState<'bulk' | 'individual'>('bulk');
  const [userEmail, setUserEmail] = useState('');
  const [userName, setUserName] = useState('');

  useEffect(() => {
    loadCampaignData();
  }, []);

  const loadCampaignData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [scheduleData, previewData] = await Promise.all([
        campaignService.getCampaignSchedule(),
        campaignService.getNewUserCampaignsPreview()
      ]);
      
      setSchedule(scheduleData);
      setNewUserPreview(previewData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load campaign data');
    } finally {
      setLoading(false);
    }
  };

  const handleSendCampaign = async () => {
    if (!selectedCampaign) return;
    
    try {
      setSendingCampaign(selectedCampaign);
      setError(null);

      const request: CampaignSendRequest = {
        campaign_type: selectedCampaign,
        ...(sendType === 'individual' && {
          user_email: userEmail,
          user_name: userName
        })
      };

      const response = await campaignService.sendCampaign(request);
      
      if (response.success) {
        alert(`✅ ${response.message}`);
        // Reset form
        setUserEmail('');
        setUserName('');
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send campaign');
    } finally {
      setSendingCampaign(null);
    }
  };

  const handleTestCampaign = async (campaignType: string) => {
    try {
      setSendingCampaign(campaignType);
      setError(null);
      
      const response = await campaignService.sendTestCampaign(campaignType);
      
      if (response.success) {
        alert(`✅ Test campaign sent: ${response.message}`);
      } else {
        throw new Error(response.message);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send test campaign');
    } finally {
      setSendingCampaign(null);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {[
            { key: 'schedule', label: 'Campaign Schedule' },
            { key: 'send', label: 'Send Campaign' },
            { key: 'preview', label: 'New User Preview' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6">
        {error && <ErrorMessage message={error} />}

        {activeTab === 'schedule' && schedule && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium text-gray-900">Campaign Schedule Overview</h3>
              <button
                onClick={loadCampaignData}
                className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100"
              >
                Refresh
              </button>
            </div>

            <div className="grid gap-4">
              {Object.entries(schedule.campaigns).map(([type, campaign]) => {
                const isDue = campaignService.isCampaignDue(campaign.schedule);
                const statusColor = campaignService.getStatusColor(campaign.status, isDue);
                
                return (
                  <div key={type} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h4 className="font-medium text-gray-900 capitalize">
                            {type.replace('_', ' ')}
                          </h4>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${statusColor}`}>
                            {campaign.status}
                          </span>
                          {isDue && (
                            <span className="px-2 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-800">
                              Due
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{campaign.subject}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          Schedule: {campaignService.formatSchedule(campaign.schedule)}
                        </p>
                      </div>
                      <button
                        onClick={() => handleTestCampaign(type)}
                        disabled={sendingCampaign === type}
                        className="ml-4 px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 disabled:opacity-50"
                      >
                        {sendingCampaign === type ? 'Sending...' : 'Test Send'}
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>

            {schedule.due_campaigns.length > 0 && (
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <h4 className="font-medium text-orange-900">Due Campaigns</h4>
                <p className="text-sm text-orange-700 mt-1">
                  The following campaigns are due to be sent: {schedule.due_campaigns.join(', ')}
                </p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'send' && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">Send Campaign Manually</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Campaign Type
                </label>
                <select
                  value={selectedCampaign}
                  onChange={(e) => setSelectedCampaign(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {campaignService.getCampaignTypes().map((type) => (
                    <option key={type} value={type}>
                      {type.replace('_', ' ').toUpperCase()}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Send Type
                </label>
                <select
                  value={sendType}
                  onChange={(e) => setSendType(e.target.value as 'bulk' | 'individual')}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="bulk">Bulk (All Users)</option>
                  <option value="individual">Individual User</option>
                </select>
              </div>
            </div>

            {sendType === 'individual' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    User Email
                  </label>
                  <input
                    type="email"
                    value={userEmail}
                    onChange={(e) => setUserEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    User Name
                  </label>
                  <input
                    type="text"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    placeholder="John Doe"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            )}

            <button
              onClick={handleSendCampaign}
              disabled={
                sendingCampaign !== null || 
                (sendType === 'individual' && (!userEmail || !userName))
              }
              className="w-full md:w-auto px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {sendingCampaign ? 'Sending...' : `Send ${sendType === 'bulk' ? 'Bulk' : 'Individual'} Campaign`}
            </button>
          </div>
        )}

        {activeTab === 'preview' && newUserPreview && (
          <div className="space-y-6">
            <h3 className="text-lg font-medium text-gray-900">New User Campaign Preview</h3>
            
            <div className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900">Instant Email</h4>
                <p className="text-sm text-green-700 mt-1">
                  <strong>{newUserPreview.instant_email.subject}</strong>
                </p>
                <p className="text-xs text-green-600 mt-1">
                  {newUserPreview.instant_email.note}
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900">
                  Future Campaigns ({newUserPreview.future_campaigns.count})
                </h4>
                <p className="text-xs text-blue-600 mb-3">
                  {newUserPreview.future_campaigns.note}
                </p>
                <div className="space-y-2">
                  {newUserPreview.future_campaigns.campaigns.map((campaign) => (
                    <div key={campaign.campaign_type} className="bg-white rounded p-3">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-gray-900">{campaign.subject}</p>
                          <p className="text-sm text-gray-600">
                            {campaignService.formatSchedule(campaign.schedule)}
                          </p>
                        </div>
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          {campaign.days_from_now} days
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {newUserPreview.past_campaigns.count > 0 && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900">
                    Past Campaigns ({newUserPreview.past_campaigns.count})
                  </h4>
                  <p className="text-xs text-gray-600 mb-2">
                    {newUserPreview.past_campaigns.note}
                  </p>
                  <p className="text-sm text-gray-700">
                    {newUserPreview.past_campaigns.campaigns.join(', ')}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CampaignManagement;
