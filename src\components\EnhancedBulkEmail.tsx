import React, { useState, useEffect } from 'react';
import { adminService } from '../services/adminService';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

interface BulkEmailFilters {
  minPoints: number;
  status: 'all' | 'active' | 'inactive';
  registrationDateRange: {
    start: string;
    end: string;
  };
  includeAdmins: boolean;
}

const EnhancedBulkEmail: React.FC = () => {
  const [emailData, setEmailData] = useState({
    subject: '',
    body: ''
  });

  const [filters, setFilters] = useState<BulkEmailFilters>({
    minPoints: 0,
    status: 'all',
    registrationDateRange: {
      start: '',
      end: ''
    },
    includeAdmins: false
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [previewMode, setPreviewMode] = useState(false);
  const [estimatedRecipients, setEstimatedRecipients] = useState<number | null>(null);

  // Email templates
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const emailTemplates = {
    welcome: {
      subject: 'Welcome to LawVriksh!',
      body: 'Dear {name},\n\nWelcome to LawVriksh! We\'re excited to have you on board.\n\nBest regards,\nThe LawVriksh Team'
    },
    update: {
      subject: 'Important Update from LawVriksh',
      body: 'Dear {name},\n\nWe have an important update to share with you.\n\n[Your update content here]\n\nBest regards,\nThe LawVriksh Team'
    },
    promotion: {
      subject: 'Special Promotion for LawVriksh Members',
      body: 'Dear {name},\n\nWe have a special promotion just for you!\n\n[Promotion details here]\n\nBest regards,\nThe LawVriksh Team'
    }
  };

  useEffect(() => {
    // Estimate recipients when filters change
    estimateRecipients();
  }, [filters]);

  const estimateRecipients = async () => {
    try {
      // This would normally call an API endpoint to get recipient count
      // For now, we'll use a mock calculation
      let count = 100; // Base user count
      
      if (filters.status === 'active') count = Math.floor(count * 0.8);
      if (filters.status === 'inactive') count = Math.floor(count * 0.2);
      if (filters.minPoints > 0) count = Math.floor(count * 0.6);
      if (!filters.includeAdmins) count = Math.floor(count * 0.95);
      
      setEstimatedRecipients(count);
    } catch (err) {
      console.error('Failed to estimate recipients:', err);
    }
  };

  const handleTemplateSelect = (templateKey: string) => {
    if (templateKey && emailTemplates[templateKey as keyof typeof emailTemplates]) {
      const template = emailTemplates[templateKey as keyof typeof emailTemplates];
      setEmailData({
        subject: template.subject,
        body: template.body
      });
      setSelectedTemplate(templateKey);
    }
  };

  const handleSendEmail = async () => {
    if (!emailData.subject || !emailData.body) {
      setError('Please fill in both subject and body');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      const bulkEmailRequest = {
        subject: emailData.subject,
        body: emailData.body,
        min_points: filters.minPoints
      };

      const result = await adminService.sendBulkEmail(bulkEmailRequest);
      
      if (result) {
        setSuccess(`Email sent successfully to ${result.recipients} recipients!`);
        // Reset form
        setEmailData({ subject: '', body: '' });
        setSelectedTemplate('');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send bulk email');
    } finally {
      setLoading(false);
    }
  };

  const handlePreview = () => {
    setPreviewMode(true);
  };

  const formatEmailBody = (body: string) => {
    // Replace placeholders with sample data for preview
    return body
      .replace(/{name}/g, 'John Doe')
      .replace(/{email}/g, '<EMAIL>')
      .replace(/{points}/g, '25');
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      <div className="border-b border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900">Enhanced Bulk Email Campaign</h3>
        <p className="text-sm text-gray-600 mt-1">
          Send targeted emails to users based on various criteria
        </p>
      </div>

      <div className="p-6 space-y-6">
        {error && <ErrorMessage message={error} />}
        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-green-800">{success}</p>
              </div>
            </div>
          </div>
        )}

        {/* Email Templates */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Templates (Optional)
          </label>
          <select
            value={selectedTemplate}
            onChange={(e) => handleTemplateSelect(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select a template...</option>
            <option value="welcome">Welcome Email</option>
            <option value="update">Important Update</option>
            <option value="promotion">Special Promotion</option>
          </select>
        </div>

        {/* Email Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Subject Line
              </label>
              <input
                type="text"
                value={emailData.subject}
                onChange={(e) => setEmailData(prev => ({ ...prev, subject: e.target.value }))}
                placeholder="Enter email subject"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email Body
              </label>
              <textarea
                value={emailData.body}
                onChange={(e) => setEmailData(prev => ({ ...prev, body: e.target.value }))}
                placeholder="Enter email content. Use {name}, {email}, {points} as placeholders."
                rows={8}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              />
              <p className="text-xs text-gray-500 mt-1">
                Available placeholders: {'{name}'}, {'{email}'}, {'{points}'}
              </p>
            </div>
          </div>

          {/* Recipient Filters */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-900">Recipient Filters</h4>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Points
              </label>
              <input
                type="number"
                value={filters.minPoints}
                onChange={(e) => setFilters(prev => ({ ...prev, minPoints: parseInt(e.target.value) || 0 }))}
                min="0"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                User Status
              </label>
              <select
                value={filters.status}
                onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value as any }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Users</option>
                <option value="active">Active Users Only</option>
                <option value="inactive">Inactive Users Only</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Registration Date Range
              </label>
              <div className="grid grid-cols-2 gap-2">
                <input
                  type="date"
                  value={filters.registrationDateRange.start}
                  onChange={(e) => setFilters(prev => ({ 
                    ...prev, 
                    registrationDateRange: { ...prev.registrationDateRange, start: e.target.value }
                  }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <input
                  type="date"
                  value={filters.registrationDateRange.end}
                  onChange={(e) => setFilters(prev => ({ 
                    ...prev, 
                    registrationDateRange: { ...prev.registrationDateRange, end: e.target.value }
                  }))}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="includeAdmins"
                checked={filters.includeAdmins}
                onChange={(e) => setFilters(prev => ({ ...prev, includeAdmins: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="includeAdmins" className="ml-2 text-sm text-gray-700">
                Include admin users
              </label>
            </div>

            {/* Estimated Recipients */}
            {estimatedRecipients !== null && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <p className="text-sm text-blue-800">
                  <strong>Estimated Recipients:</strong> {estimatedRecipients} users
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex space-x-4">
          <button
            onClick={handlePreview}
            disabled={!emailData.subject || !emailData.body}
            className="px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 disabled:opacity-50"
          >
            Preview Email
          </button>
          <button
            onClick={handleSendEmail}
            disabled={loading || !emailData.subject || !emailData.body}
            className="px-6 py-2 bg-blue-600 text-white font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="flex items-center">
                <LoadingSpinner size="small" />
                <span className="ml-2">Sending...</span>
              </div>
            ) : (
              'Send Bulk Email'
            )}
          </button>
        </div>

        {/* Preview Modal */}
        {previewMode && (
          <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-2xl shadow-lg rounded-md bg-white">
              <div className="mt-3">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Email Preview</h3>
                
                <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
                  <div className="mb-4">
                    <strong className="text-sm text-gray-700">Subject:</strong>
                    <p className="text-gray-900">{emailData.subject}</p>
                  </div>
                  
                  <div>
                    <strong className="text-sm text-gray-700">Body:</strong>
                    <div className="mt-2 p-3 bg-white border rounded whitespace-pre-wrap">
                      {formatEmailBody(emailData.body)}
                    </div>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={() => setPreviewMode(false)}
                    className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
                  >
                    Close Preview
                  </button>
                  <button
                    onClick={() => {
                      setPreviewMode(false);
                      handleSendEmail();
                    }}
                    disabled={loading}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                  >
                    Send Email
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedBulkEmail;
