import React, { useState, useEffect } from 'react';
import { shareAnalyticsService } from '../services/shareAnalyticsService';
import { ShareAnalyticsEnhanced, ShareHistoryResponse } from '../types/api';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';

const ShareAnalytics: React.FC = () => {
  const [analytics, setAnalytics] = useState<ShareAnalyticsEnhanced | null>(null);
  const [shareHistory, setShareHistory] = useState<ShareHistoryResponse | null>(null);
  const [platformStats, setPlatformStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'history' | 'platforms'>('overview');

  // Filters
  const [selectedPlatform, setSelectedPlatform] = useState<string>('');
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year' | 'all'>('month');
  const [historyPage, setHistoryPage] = useState(1);

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPlatform, selectedPeriod]);

  useEffect(() => {
    if (activeTab === 'history') {
      loadShareHistory();
    } else if (activeTab === 'platforms') {
      loadPlatformStats();
    }
  }, [activeTab, historyPage]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const analyticsData = await shareAnalyticsService.getShareAnalytics({
        period: selectedPeriod,
        platform: selectedPlatform || undefined
      });
      
      setAnalytics(analyticsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics');
    } finally {
      setLoading(false);
    }
  };

  const loadShareHistory = async () => {
    try {
      const historyData = await shareAnalyticsService.getShareHistory({
        page: historyPage,
        limit: 20,
        platform: selectedPlatform || undefined
      });
      
      setShareHistory(historyData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load share history');
    }
  };

  const loadPlatformStats = async () => {
    try {
      const stats = await shareAnalyticsService.getPlatformStatistics();
      setPlatformStats(stats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load platform statistics');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEngagementMetrics = () => {
    if (!analytics) return null;
    return shareAnalyticsService.calculateEngagementMetrics(analytics);
  };

  if (loading && !analytics) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner />
      </div>
    );
  }

  const engagementMetrics = getEngagementMetrics();

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200">
      {/* Header */}
      <div className="border-b border-gray-200">
        <div className="flex justify-between items-center p-6">
          <h3 className="text-lg font-medium text-gray-900">Share Analytics & History</h3>
          <div className="flex space-x-4">
            <select
              value={selectedPlatform}
              onChange={(e) => setSelectedPlatform(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Platforms</option>
              <option value="linkedin">LinkedIn</option>
              <option value="facebook">Facebook</option>
              <option value="twitter">Twitter</option>
              <option value="instagram">Instagram</option>
              <option value="whatsapp">WhatsApp</option>
            </select>
            
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="quarter">Last Quarter</option>
              <option value="year">Last Year</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>

        {/* Tabs */}
        <nav className="flex space-x-8 px-6">
          {[
            { key: 'overview', label: 'Overview' },
            { key: 'history', label: 'Share History' },
            { key: 'platforms', label: 'Platform Stats' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6">
        {error && <ErrorMessage message={error} />}

        {activeTab === 'overview' && analytics && (
          <div className="space-y-6">
            {/* Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-900">Total Shares</h4>
                <p className="text-2xl font-bold text-blue-600">{analytics.summary.total_shares}</p>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-green-900">Total Points</h4>
                <p className="text-2xl font-bold text-green-600">{analytics.summary.total_points}</p>
              </div>
              <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-purple-900">Active Platforms</h4>
                <p className="text-2xl font-bold text-purple-600">{analytics.summary.active_platforms}</p>
              </div>
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-orange-900">Avg Points/Share</h4>
                <p className="text-2xl font-bold text-orange-600">
                  {analytics.summary.average_points_per_share.toFixed(1)}
                </p>
              </div>
            </div>

            {/* Engagement Insights */}
            {engagementMetrics && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 className="text-lg font-medium text-gray-900 mb-3">Engagement Insights</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Most Active Platform:</span>
                    <p className="font-medium">
                      {shareAnalyticsService.formatPlatformName(engagementMetrics.mostActivePlatform.name)} 
                      ({engagementMetrics.mostActivePlatform.shares} shares)
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-600">Highest Earning:</span>
                    <p className="font-medium">
                      {shareAnalyticsService.formatPlatformName(engagementMetrics.highestEarningPlatform.name)} 
                      ({engagementMetrics.highestEarningPlatform.points} points)
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-600">Most Efficient:</span>
                    <p className="font-medium">
                      {shareAnalyticsService.formatPlatformName(engagementMetrics.mostEfficientPlatform.platform)} 
                      ({engagementMetrics.mostEfficientPlatform.efficiency.toFixed(1)} pts/share)
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Platform Breakdown */}
            <div>
              <h4 className="text-lg font-medium text-gray-900 mb-4">Platform Breakdown</h4>
              <div className="grid gap-4">
                {Object.entries(analytics.platform_breakdown).map(([platform, data]) => (
                  <div key={platform} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{shareAnalyticsService.getPlatformIcon(platform)}</span>
                        <h5 className="font-medium text-gray-900">
                          {shareAnalyticsService.formatPlatformName(platform)}
                        </h5>
                      </div>
                      <span className="text-sm text-gray-500">{data.percentage.toFixed(1)}%</span>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-600">Shares:</span>
                        <p className="font-medium">{data.shares}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">Points:</span>
                        <p className="font-medium">{data.points}</p>
                      </div>
                      <div>
                        <span className="text-gray-600">First Share:</span>
                        <p className="font-medium">
                          {data.first_share_date ? formatDate(data.first_share_date) : 'N/A'}
                        </p>
                      </div>
                      <div>
                        <span className="text-gray-600">Last Share:</span>
                        <p className="font-medium">
                          {data.last_share_date ? formatDate(data.last_share_date) : 'N/A'}
                        </p>
                      </div>
                    </div>
                    
                    {/* Progress bar */}
                    <div className="mt-3">
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="h-2 rounded-full"
                          style={{ 
                            width: `${data.percentage}%`,
                            backgroundColor: shareAnalyticsService.getPlatformColor(platform)
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Timeline Chart */}
            {analytics.timeline.length > 0 && (
              <div>
                <h4 className="text-lg font-medium text-gray-900 mb-4">Share Timeline</h4>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-7 gap-2">
                    {analytics.timeline.slice(-7).map((day, index) => (
                      <div key={index} className="text-center">
                        <div className="text-xs text-gray-600 mb-1">
                          {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}
                        </div>
                        <div className="bg-blue-100 rounded p-2">
                          <div className="text-sm font-medium text-blue-900">{day.shares}</div>
                          <div className="text-xs text-blue-600">{day.points} pts</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'history' && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900">Share History</h4>
            
            {shareHistory ? (
              <>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-blue-700">Total Shares:</span>
                      <p className="font-medium text-blue-900">{shareHistory.summary.total_shares}</p>
                    </div>
                    <div>
                      <span className="text-blue-700">Points Earned:</span>
                      <p className="font-medium text-blue-900">{shareHistory.summary.total_points_earned}</p>
                    </div>
                    <div>
                      <span className="text-blue-700">Platforms Used:</span>
                      <p className="font-medium text-blue-900">{shareHistory.summary.platforms_used.length}</p>
                    </div>
                  </div>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Platform
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Points Earned
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Date & Time
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {shareHistory.shares.map((share) => (
                        <tr key={share.share_id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <span className="text-lg mr-2">
                                {shareAnalyticsService.getPlatformIcon(share.platform)}
                              </span>
                              <span className="text-sm font-medium text-gray-900">
                                {shareAnalyticsService.formatPlatformName(share.platform)}
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              share.points_earned > 0 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {share.points_earned > 0 ? `+${share.points_earned}` : '0'} points
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {formatDate(share.timestamp)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing {((shareHistory.pagination.page - 1) * shareHistory.pagination.limit) + 1} to{' '}
                    {Math.min(shareHistory.pagination.page * shareHistory.pagination.limit, shareHistory.pagination.total)} of{' '}
                    {shareHistory.pagination.total} shares
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setHistoryPage(Math.max(1, historyPage - 1))}
                      disabled={historyPage === 1}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                    >
                      Previous
                    </button>
                    <span className="px-3 py-2 text-sm font-medium text-gray-700">
                      Page {shareHistory.pagination.page} of {shareHistory.pagination.pages}
                    </span>
                    <button
                      onClick={() => setHistoryPage(Math.min(shareHistory.pagination.pages, historyPage + 1))}
                      disabled={historyPage === shareHistory.pagination.pages}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <LoadingSpinner />
                <p className="text-gray-500 mt-2">Loading share history...</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'platforms' && (
          <div className="space-y-4">
            <h4 className="text-lg font-medium text-gray-900">Platform Statistics</h4>
            
            {platformStats ? (
              <div className="grid gap-4">
                {Object.entries(platformStats).map(([platform, stats]: [string, any]) => (
                  <div key={platform} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <span className="text-2xl">{shareAnalyticsService.getPlatformIcon(platform)}</span>
                      <h5 className="text-lg font-medium text-gray-900">
                        {shareAnalyticsService.formatPlatformName(platform)}
                      </h5>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">{stats.total_shares}</p>
                        <p className="text-sm text-gray-600">Total Shares</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">{stats.total_points}</p>
                        <p className="text-sm text-gray-600">Total Points</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-purple-600">{stats.unique_users}</p>
                        <p className="text-sm text-gray-600">Unique Users</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-orange-600">
                          {stats.average_points_per_user.toFixed(1)}
                        </p>
                        <p className="text-sm text-gray-600">Avg Points/User</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <LoadingSpinner />
                <p className="text-gray-500 mt-2">Loading platform statistics...</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ShareAnalytics;
