import React, { useState, useEffect } from 'react';
import { shareAnalyticsService } from '../services/shareAnalyticsService';
import { ShareAnalyticsEnhanced, ShareHistoryResponse } from '../types/api';
import LoadingSpinner from './LoadingSpinner';
import ErrorMessage from './ErrorMessage';
import './ShareAnalytics.css';

const ShareAnalytics: React.FC = () => {
  const [analytics, setAnalytics] = useState<ShareAnalyticsEnhanced | null>(null);
  const [shareHistory, setShareHistory] = useState<ShareHistoryResponse | null>(null);
  const [platformStats, setPlatformStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'history' | 'platforms'>('overview');

  // Filters
  const [selectedPlatform, setSelectedPlatform] = useState<string>('');
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year' | 'all'>('month');
  const [historyPage, setHistoryPage] = useState(1);

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedPlatform, selectedPeriod]);

  useEffect(() => {
    if (activeTab === 'history') {
      loadShareHistory();
    } else if (activeTab === 'platforms') {
      loadPlatformStats();
    }
  }, [activeTab, historyPage]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const analyticsData = await shareAnalyticsService.getShareAnalytics({
        period: selectedPeriod,
        platform: selectedPlatform || undefined
      });
      
      setAnalytics(analyticsData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics');
    } finally {
      setLoading(false);
    }
  };

  const loadShareHistory = async () => {
    try {
      const historyData = await shareAnalyticsService.getShareHistory({
        page: historyPage,
        limit: 20,
        platform: selectedPlatform || undefined
      });
      
      setShareHistory(historyData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load share history');
    }
  };

  const loadPlatformStats = async () => {
    try {
      const stats = await shareAnalyticsService.getPlatformStatistics();
      setPlatformStats(stats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load platform statistics');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEngagementMetrics = () => {
    if (!analytics) return null;
    return shareAnalyticsService.calculateEngagementMetrics(analytics);
  };

  if (loading && !analytics) {
    return (
      <div className="share-analytics-loading">
        <LoadingSpinner />
      </div>
    );
  }

  const engagementMetrics = getEngagementMetrics();

  return (
    <div className="share-analytics-container">
      {/* Header */}
      <div className="share-analytics-header">
        <div className="share-analytics-header-content">
          <h3 className="share-analytics-title">Share Analytics & History</h3>
          <div className="share-analytics-controls">
            <select
              value={selectedPlatform}
              onChange={(e) => setSelectedPlatform(e.target.value)}
              className="share-analytics-select"
            >
              <option value="">All Platforms</option>
              <option value="linkedin">LinkedIn</option>
              <option value="facebook">Facebook</option>
              <option value="twitter">Twitter</option>
              <option value="instagram">Instagram</option>
              <option value="whatsapp">WhatsApp</option>
            </select>

            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value as any)}
              className="share-analytics-select"
            >
              <option value="week">Last Week</option>
              <option value="month">Last Month</option>
              <option value="quarter">Last Quarter</option>
              <option value="year">Last Year</option>
              <option value="all">All Time</option>
            </select>
          </div>
        </div>

        {/* Tabs */}
        <nav className="share-analytics-tabs">
          {[
            { key: 'overview', label: 'Overview' },
            { key: 'history', label: 'Share History' },
            { key: 'platforms', label: 'Platform Stats' }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`share-analytics-tab ${activeTab === tab.key ? 'active' : ''}`}
            >
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      <div className="share-analytics-content">
        {error && <ErrorMessage message={error} />}

        {activeTab === 'overview' && analytics && (
          <div className="overview-section">
            {/* Summary Cards */}
            <div className="summary-cards">
              <div className="summary-card blue">
                <h4 className="summary-card-title">Total Shares</h4>
                <p className="summary-card-value">{analytics.summary.total_shares}</p>
              </div>
              <div className="summary-card green">
                <h4 className="summary-card-title">Total Points</h4>
                <p className="summary-card-value">{analytics.summary.total_points}</p>
              </div>
              <div className="summary-card purple">
                <h4 className="summary-card-title">Active Platforms</h4>
                <p className="summary-card-value">{analytics.summary.active_platforms}</p>
              </div>
              <div className="summary-card orange">
                <h4 className="summary-card-title">Avg Points/Share</h4>
                <p className="summary-card-value">
                  {analytics.summary.average_points_per_share.toFixed(1)}
                </p>
              </div>
            </div>

            {/* Engagement Insights */}
            {engagementMetrics && (
              <div className="engagement-insights">
                <h4 className="engagement-insights-title">Engagement Insights</h4>
                <div className="engagement-insights-grid">
                  <div className="engagement-insight-item">
                    <span className="engagement-insight-label">Most Active Platform:</span>
                    <p className="engagement-insight-value">
                      {shareAnalyticsService.formatPlatformName(engagementMetrics.mostActivePlatform.name)}
                      ({engagementMetrics.mostActivePlatform.shares} shares)
                    </p>
                  </div>
                  <div className="engagement-insight-item">
                    <span className="engagement-insight-label">Highest Earning:</span>
                    <p className="engagement-insight-value">
                      {shareAnalyticsService.formatPlatformName(engagementMetrics.highestEarningPlatform.name)}
                      ({engagementMetrics.highestEarningPlatform.points} points)
                    </p>
                  </div>
                  <div className="engagement-insight-item">
                    <span className="engagement-insight-label">Most Efficient:</span>
                    <p className="engagement-insight-value">
                      {shareAnalyticsService.formatPlatformName(engagementMetrics.mostEfficientPlatform.platform)}
                      ({engagementMetrics.mostEfficientPlatform.efficiency.toFixed(1)} pts/share)
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Platform Breakdown */}
            <div className="platform-breakdown-section">
              <h4 className="platform-breakdown-title">Platform Breakdown</h4>
              <div className="platform-breakdown-grid">
                {Object.entries(analytics.platform_breakdown).map(([platform, data]) => (
                  <div key={platform} className="platform-breakdown-item">
                    <div className="platform-breakdown-header">
                      <div className="platform-breakdown-name-section">
                        <span className="platform-breakdown-icon">{shareAnalyticsService.getPlatformIcon(platform)}</span>
                        <h5 className="platform-breakdown-name">
                          {shareAnalyticsService.formatPlatformName(platform)}
                        </h5>
                      </div>
                      <span className="platform-breakdown-percentage">{data.percentage.toFixed(1)}%</span>
                    </div>

                    <div className="platform-breakdown-stats">
                      <div className="platform-breakdown-stat">
                        <span className="platform-breakdown-stat-label">Shares:</span>
                        <p className="platform-breakdown-stat-value">{data.shares}</p>
                      </div>
                      <div className="platform-breakdown-stat">
                        <span className="platform-breakdown-stat-label">Points:</span>
                        <p className="platform-breakdown-stat-value">{data.points}</p>
                      </div>
                      <div className="platform-breakdown-stat">
                        <span className="platform-breakdown-stat-label">First Share:</span>
                        <p className="platform-breakdown-stat-value">
                          {data.first_share_date ? formatDate(data.first_share_date) : 'N/A'}
                        </p>
                      </div>
                      <div className="platform-breakdown-stat">
                        <span className="platform-breakdown-stat-label">Last Share:</span>
                        <p className="platform-breakdown-stat-value">
                          {data.last_share_date ? formatDate(data.last_share_date) : 'N/A'}
                        </p>
                      </div>
                    </div>

                    {/* Progress bar */}
                    <div className="platform-breakdown-progress">
                      <div className="platform-breakdown-progress-bar">
                        <div
                          className="platform-breakdown-progress-fill"
                          style={{
                            width: `${data.percentage}%`,
                            backgroundColor: shareAnalyticsService.getPlatformColor(platform)
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Timeline Chart */}
            {analytics.timeline.length > 0 && (
              <div className="timeline-section">
                <h4 className="timeline-title">Share Timeline</h4>
                <div className="timeline-chart">
                  <div className="timeline-grid">
                    {analytics.timeline.slice(-7).map((day, index) => (
                      <div key={index} className="timeline-day">
                        <div className="timeline-day-label">
                          {new Date(day.date).toLocaleDateString('en-US', { weekday: 'short' })}
                        </div>
                        <div className="timeline-day-data">
                          <div className="timeline-day-shares">{day.shares}</div>
                          <div className="timeline-day-points">{day.points} pts</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'history' && (
          <div className="history-section">
            <h4 className="history-title">Share History</h4>

            {shareHistory ? (
              <>
                <div className="history-summary">
                  <div className="history-summary-grid">
                    <div className="history-summary-item">
                      <span className="history-summary-label">Total Shares:</span>
                      <p className="history-summary-value">{shareHistory.pagination?.total || 0}</p>
                    </div>
                    <div className="history-summary-item">
                      <span className="history-summary-label">Points Earned:</span>
                      <p className="history-summary-value">{shareHistory.shares.reduce((sum, share) => sum + share.points_earned, 0)}</p>
                    </div>
                    <div className="history-summary-item">
                      <span className="history-summary-label">Platforms Used:</span>
                      <p className="history-summary-value">{new Set(shareHistory.shares.map(s => s.platform)).size}</p>
                    </div>
                  </div>
                </div>

                <div className="share-analytics-table-container">
                  <table className="share-analytics-table">
                    <thead>
                      <tr>
                        <th>Platform</th>
                        <th>Points Earned</th>
                        <th>Date & Time</th>
                      </tr>
                    </thead>
                    <tbody>
                      {shareHistory.shares.map((share) => (
                        <tr key={share.share_id}>
                          <td>
                            <div className="platform-cell">
                              <span className="platform-icon">
                                {shareAnalyticsService.getPlatformIcon(share.platform)}
                              </span>
                              <span className="platform-name">
                                {shareAnalyticsService.formatPlatformName(share.platform)}
                              </span>
                            </div>
                          </td>
                          <td>
                            <span className={`points-badge ${share.points_earned > 0 ? 'positive' : 'zero'}`}>
                              {share.points_earned > 0 ? `+${share.points_earned}` : '0'} points
                            </span>
                          </td>
                          <td className="timestamp-cell">
                            {formatDate(share.timestamp)}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                {/* Pagination */}
                <div className="share-analytics-pagination">
                  <div className="pagination-info">
                    Showing {((shareHistory.pagination.page - 1) * shareHistory.pagination.limit) + 1} to{' '}
                    {Math.min(shareHistory.pagination.page * shareHistory.pagination.limit, shareHistory.pagination.total)} of{' '}
                    {shareHistory.pagination.total} shares
                  </div>
                  <div className="pagination-controls">
                    <button
                      onClick={() => setHistoryPage(Math.max(1, historyPage - 1))}
                      disabled={historyPage === 1}
                      className="pagination-button"
                    >
                      Previous
                    </button>
                    <span className="pagination-current">
                      Page {shareHistory.pagination.page} of {shareHistory.pagination.pages}
                    </span>
                    <button
                      onClick={() => setHistoryPage(Math.min(shareHistory.pagination.pages, historyPage + 1))}
                      disabled={historyPage === shareHistory.pagination.pages}
                      className="pagination-button"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <div className="section-loading">
                <LoadingSpinner />
                <p className="section-loading-text">Loading share history...</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'platforms' && (
          <div className="history-section">
            <h4 className="history-title">Platform Statistics</h4>

            {platformStats ? (
              <div className="platform-breakdown-grid">
                {Object.entries(platformStats).map(([platform, stats]: [string, any]) => (
                  <div key={platform} className="platform-breakdown-item">
                    <div className="platform-breakdown-name-section">
                      <span className="platform-breakdown-icon" style={{ fontSize: '1.5rem' }}>{shareAnalyticsService.getPlatformIcon(platform)}</span>
                      <h5 className="platform-breakdown-name" style={{ fontSize: '1.125rem' }}>
                        {shareAnalyticsService.formatPlatformName(platform)}
                      </h5>
                    </div>

                    <div className="platform-breakdown-stats" style={{ gridTemplateColumns: 'repeat(4, 1fr)', textAlign: 'center' }}>
                      <div className="platform-breakdown-stat">
                        <p className="summary-card-value blue" style={{ fontSize: '1.5rem' }}>{stats.total_shares}</p>
                        <p className="platform-breakdown-stat-label">Total Shares</p>
                      </div>
                      <div className="platform-breakdown-stat">
                        <p className="summary-card-value green" style={{ fontSize: '1.5rem' }}>{stats.total_points}</p>
                        <p className="platform-breakdown-stat-label">Total Points</p>
                      </div>
                      <div className="platform-breakdown-stat">
                        <p className="summary-card-value purple" style={{ fontSize: '1.5rem' }}>{stats.unique_users}</p>
                        <p className="platform-breakdown-stat-label">Unique Users</p>
                      </div>
                      <div className="platform-breakdown-stat">
                        <p className="summary-card-value orange" style={{ fontSize: '1.5rem' }}>
                          {stats.average_points_per_user.toFixed(1)}
                        </p>
                        <p className="platform-breakdown-stat-label">Avg Points/User</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="section-loading">
                <LoadingSpinner />
                <p className="section-loading-text">Loading platform statistics...</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ShareAnalytics;
