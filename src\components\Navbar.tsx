import React, { useEffect, useState } from 'react';
import Button from './Button';
import './Navbar.css';

interface NavbarProps {
  onJoinWaitlist?: () => void;
}

const Navigation = () => {
  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, targetId: string) => {
    e.preventDefault();
    const targetElement = document.getElementById(targetId);
    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  return (
    <div className="navbar__nav">
      <a href="#home" className="navbar__nav-link" onClick={(e) => handleSmoothScroll(e, 'home')}>Home</a>
      <span className="navbar__nav-separator"> • </span>
      <a href="#features" className="navbar__nav-link" onClick={(e) => handleSmoothScroll(e, 'features')}>Features</a>
      <span className="navbar__nav-separator"> • </span>
      <a href="#contact" className="navbar__nav-link" onClick={(e) => handleSmoothScroll(e, 'contact')}>Contact Us</a>
    </div>
  );
};

export default function Navbar({ onJoinWaitlist }: NavbarProps) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      const threshold = 80; // Scroll threshold in pixels

      if (scrollPosition > threshold) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    // Add scroll event listener
    window.addEventListener('scroll', handleScroll);

    // Cleanup function to remove event listener
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <nav className={`navbar ${isScrolled ? 'navbar--scrolled' : ''}`}>
      <div className="navbar__brand">
        LawVriksh
      </div>

      <div className="navbar__content">
        <Navigation />

        <Button onClick={onJoinWaitlist}>
          Join Waitlist
        </Button>
      </div>
    </nav>
  );
}
